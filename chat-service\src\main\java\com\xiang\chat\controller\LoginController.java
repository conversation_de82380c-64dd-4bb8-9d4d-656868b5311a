package com.xiang.chat.controller;

import com.xiang.chat.service.OAuth2TokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Slf4j
@Controller
public class LoginController {

    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    @GetMapping("/login")
    public String login() {
        return "login";
    }

    @GetMapping("/chat")
    public String chat(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            String username = authentication.getName();
            model.addAttribute("username", username);

            // 获取OAuth2 token信息并传递到页面
            if (authentication.getDetails() instanceof OAuth2TokenResponse tokenResponse) {
                String accessToken = tokenResponse.getAccessToken();
                String tokenType = tokenResponse.getTokenType();
                Integer expiresIn = tokenResponse.getExpiresIn();

                model.addAttribute("accessToken", accessToken);
                model.addAttribute("tokenType", tokenType);
                model.addAttribute("expiresIn", expiresIn);

                log.info("User {} accessed chat page with JWT token: tokenType={}, tokenLength={}, expiresIn={}",
                    username, tokenType, accessToken != null ? accessToken.length() : 0, expiresIn);
            } else {
                log.warn("User {} accessed chat page without JWT token, details type: {}",
                    username, authentication.getDetails() != null ? authentication.getDetails().getClass().getSimpleName() : "null");
                // 如果没有token，可能需要重新认证
                model.addAttribute("accessToken", "");
                model.addAttribute("tokenType", "");
            }
        } else {
            model.addAttribute("username", "Guest");
            model.addAttribute("accessToken", "");
            model.addAttribute("tokenType", "");
            log.warn("Unauthenticated user trying to access chat page");
        }

        return "chat";
    }
}